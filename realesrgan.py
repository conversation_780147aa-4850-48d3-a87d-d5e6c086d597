import warnings
warnings.filterwarnings("ignore", message=".*torchvision.transforms.functional_tensor.*deprecated.*")

import cv2
import numpy
import os
import torch
import gradio as gr
from basicsr.archs.rrdbnet_arch import RRDBNet
from realesrgan import RealESRGANer
from realesrgan.archs.srvgg_arch import SRVGGNetCompact

DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
print(f"🚀 Running on device: {DEVICE}")

img_mode = "RGBA"

MODEL_MAPPING = {
    "General Purpose (4x Upscale)": "RealESRGAN_x4plus",
    "Clean Images (4x Upscale)": "RealESRNet_x4plus",
    "Anime/Illustrations (4x Upscale)": "RealESRGAN_x4plus_anime_6B",
    "General Purpose (2x Upscale)": "RealESRGAN_x2plus",
    "Universal Image Enhancer (4x, v3)": "realesr-general-x4v3"
}

def realesrgan(img, model_name, denoise_strength, outscale):
    if not img:
        return

    actual_model_name = MODEL_MAPPING.get(model_name, model_name)

    if actual_model_name == 'RealESRGAN_x4plus':
        model = RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=4)
        netscale = 4
    elif actual_model_name == 'RealESRNet_x4plus':
        model = RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=4)
        netscale = 4
    elif actual_model_name == 'RealESRGAN_x4plus_anime_6B':
        model = RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=6, num_grow_ch=32, scale=4)
        netscale = 4
    elif actual_model_name == 'RealESRGAN_x2plus':
        model = RRDBNet(num_in_ch=3, num_out_ch=3, num_feat=64, num_block=23, num_grow_ch=32, scale=2)
        netscale = 2
    elif actual_model_name == 'realesr-general-x4v3':
        model = SRVGGNetCompact(num_in_ch=3, num_out_ch=3, num_feat=64, num_conv=32, upscale=4, act_type='prelu')
        netscale = 4

    model_path = os.path.join('realesrgan', 'weights', actual_model_name + '.pth')
    if not os.path.isfile(model_path):
        raise FileNotFoundError(f"Model file not found: {model_path}. Please ensure the model file is in the realesrgan/weights folder.")

    dni_weight = None
    if actual_model_name == 'realesr-general-x4v3' and denoise_strength != 1:
        wdn_model_path = os.path.join('realesrgan', 'weights', 'realesr-general-wdn-x4v3.pth')
        if not os.path.isfile(wdn_model_path):
            raise FileNotFoundError(f"WDN model file not found: {wdn_model_path}. Please ensure the model file is in the realesrgan/weights folder.")
        model_path = [model_path, wdn_model_path]
        dni_weight = [denoise_strength, 1 - denoise_strength]

    upsampler = RealESRGANer(
        scale=netscale,
        model_path=model_path,
        dni_weight=dni_weight,
        model=model,
        tile=0,
        tile_pad=10,
        pre_pad=10,
        half=False,
        device=DEVICE
    )

    cv_img = numpy.array(img)
    img = cv2.cvtColor(cv_img, cv2.COLOR_RGBA2BGRA)

    try:
        output, _ = upsampler.enhance(img, outscale=outscale)
    except RuntimeError as error:
        print('Error', error)
        print('If you encounter CUDA out of memory, try to set --tile with a smaller number.')
        return None, "Error occurred during upscaling"
    else:
        output_img = cv2.cvtColor(output, cv2.COLOR_BGRA2RGBA) if img_mode == "RGBA" else cv2.cvtColor(output, cv2.COLOR_BGR2RGB)
        return output_img, image_properties(output_img)

def reset():
    return gr.update(value=None), gr.update(value=None), gr.update(value=None)

def has_transparency(img):
    if img.info.get("transparency", None) is not None:
        return True
    if img.mode == "P":
        transparent = img.info.get("transparency", -1)
        for _, index in img.getcolors():
            if index == transparent:
                return True
    elif img.mode == "RGBA":
        extrema = img.getextrema()
        if extrema[3][0] < 255:
            return True
    return False

def image_properties(img):
    global img_mode
    if img is None:
        return "No image data available."

    if isinstance(img, numpy.ndarray):
        height, width = img.shape[:2]
        channels = img.shape[2] if len(img.shape) > 2 else 1
        img_mode = "RGBA" if channels == 4 else "RGB" if channels == 3 else "Grayscale"
        return f"Resolution: Width: {width}, Height: {height}  |  Color Mode: {img_mode}"

    if hasattr(img, "info") and hasattr(img, "mode") and hasattr(img, "size"):
        if has_transparency(img):
            img_mode = "RGBA"
        else:
            img_mode = "RGB"
        return f"Resolution: Width: {img.size[0]}, Height: {img.size[1]}  |  Color Mode: {img_mode}"

    return "Unsupported image format."

def main():
   with gr.Blocks(
      title="Image Upscaler",
      theme=gr.themes.Soft(primary_hue="teal", secondary_hue="teal", neutral_hue="slate"),
      css="""
      @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');
      body, .gradio-container {font-family: 'Poppins', sans-serif;}
      .gradio-container {padding: 2rem;}
      """
   ) as app:

      gr.Markdown("""
      ## Image Upscaler  
      **A fast, high-quality image enhancement tool — ideal for restoring details and sharpening textures.**
      """)

        with gr.Accordion("Upscaling option"):
            with gr.Row():
                model_name = gr.Dropdown(label="Model",
                                        choices=list(MODEL_MAPPING.keys()),
                                        value="General Purpose (4x Upscale)")
                denoise_strength = gr.Slider(label="Denoise Strength", minimum=0, maximum=1, step=0.1, value=0.5)
                outscale = gr.Slider(label="Resolution Upscale", minimum=1, maximum=6, step=1, value=2)

        with gr.Row():
            with gr.Group():
                input_image = gr.Image(label="Input Image", type="pil")
                input_properties = gr.Textbox(label="Input Image Properties", interactive=False)

            with gr.Group():
                output_image = gr.Image(label="Output Image")
                output_properties = gr.Textbox(label="Output Image Properties", interactive=False)

        with gr.Row():
            reset_btn = gr.Button("Reset")
            upscale_btn = gr.Button("Upscale")

        input_image.change(fn=image_properties, inputs=input_image, outputs=input_properties)
        upscale_btn.click(fn=realesrgan,
                        inputs=[input_image, model_name, denoise_strength, outscale],
                        outputs=[output_image, output_properties])
        reset_btn.click(fn=reset, inputs=[], outputs=[input_image, output_image, input_properties])

    app.launch()

if __name__ == "__main__":
    main()