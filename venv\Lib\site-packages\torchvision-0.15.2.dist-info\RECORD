torchvision-0.15.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
torchvision-0.15.2.dist-info/LICENSE,sha256=wGNj-dM2J9xRc7E1IkRMyF-7Rzn2PhbUWH1cChZbWx4,1546
torchvision-0.15.2.dist-info/METADATA,sha256=u2AbP_xMHO-tmNYcdiC3gcbqOEKIaNl_lLoE2Cl19A0,11954
torchvision-0.15.2.dist-info/RECORD,,
torchvision-0.15.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
torchvision-0.15.2.dist-info/WHEEL,sha256=rneS2j8QNmAwdNKHN86s6-qP7AMcWZgqiEoH3bdbh_Y,102
torchvision-0.15.2.dist-info/top_level.txt,sha256=ucJZoaluBW9BGYT4TuCE6zoZY_JuSP30wbDh-IRpxUU,12
torchvision/_C.pyd,sha256=YkGP4KzoZFfkSDZfCUZNRs2W0BRUY_M_Uym3uTDR1Fc,535040
torchvision/__init__.py,sha256=biL_0F88NIVdnMTaXhUNwEmV8fbtCODQZeWqZTyKTNE,4057
torchvision/__pycache__/__init__.cpython-310.pyc,,
torchvision/__pycache__/_internally_replaced_utils.cpython-310.pyc,,
torchvision/__pycache__/_utils.cpython-310.pyc,,
torchvision/__pycache__/extension.cpython-310.pyc,,
torchvision/__pycache__/utils.cpython-310.pyc,,
torchvision/__pycache__/version.cpython-310.pyc,,
torchvision/_internally_replaced_utils.py,sha256=oas5PoFR4LlsQAe4-cHj-FNsehe-deWD4AMwq4Y672U,1801
torchvision/_utils.py,sha256=kcSn6P3Vjv5QPgHHNmNu59Mh-NLb5MDlYxvDkNJWrOM,966
torchvision/datapoints/__init__.py,sha256=-E4Jt3nli4JGI8EguieTVooUq3oCaOXr8MBlQuiYLpE,530
torchvision/datapoints/__pycache__/__init__.cpython-310.pyc,,
torchvision/datapoints/__pycache__/_bounding_box.cpython-310.pyc,,
torchvision/datapoints/__pycache__/_datapoint.cpython-310.pyc,,
torchvision/datapoints/__pycache__/_dataset_wrapper.cpython-310.pyc,,
torchvision/datapoints/__pycache__/_image.cpython-310.pyc,,
torchvision/datapoints/__pycache__/_mask.cpython-310.pyc,,
torchvision/datapoints/__pycache__/_video.cpython-310.pyc,,
torchvision/datapoints/_bounding_box.py,sha256=jn3LfNMux4Y_yFL9YLf7lZel6mumaGMdQIGEp7G1zD4,9136
torchvision/datapoints/_datapoint.py,sha256=Ye_QMdJQhh2vmD7IK0hQCSWfynu5Cm-e9GJILiyvtqc,10135
torchvision/datapoints/_dataset_wrapper.py,sha256=XBrF8otXr8nQ65HXDZkilreIoS9spnVfGb1PB2nwGeI,18167
torchvision/datapoints/_image.py,sha256=wr-0aXzGCxBxI1qPwhT2wN8mGsGJUIiDZ185jpcD5V0,10233
torchvision/datapoints/_mask.py,sha256=EgmwQr0umUg-WJNo-oHAuq5cTbof-vyFa15iHnsU0Lo,5964
torchvision/datapoints/_video.py,sha256=uBSvrlxvpAipDxdTzVSV_iAKIX-DrO_9sYHUQ53QuMM,9779
torchvision/datasets/__init__.py,sha256=s0NNqKw8Q8Ya7k74oUJESdVZm2x3EZE9ua4-Lbon6j8,3776
torchvision/datasets/__pycache__/__init__.cpython-310.pyc,,
torchvision/datasets/__pycache__/_optical_flow.cpython-310.pyc,,
torchvision/datasets/__pycache__/_stereo_matching.cpython-310.pyc,,
torchvision/datasets/__pycache__/caltech.cpython-310.pyc,,
torchvision/datasets/__pycache__/celeba.cpython-310.pyc,,
torchvision/datasets/__pycache__/cifar.cpython-310.pyc,,
torchvision/datasets/__pycache__/cityscapes.cpython-310.pyc,,
torchvision/datasets/__pycache__/clevr.cpython-310.pyc,,
torchvision/datasets/__pycache__/coco.cpython-310.pyc,,
torchvision/datasets/__pycache__/country211.cpython-310.pyc,,
torchvision/datasets/__pycache__/dtd.cpython-310.pyc,,
torchvision/datasets/__pycache__/eurosat.cpython-310.pyc,,
torchvision/datasets/__pycache__/fakedata.cpython-310.pyc,,
torchvision/datasets/__pycache__/fer2013.cpython-310.pyc,,
torchvision/datasets/__pycache__/fgvc_aircraft.cpython-310.pyc,,
torchvision/datasets/__pycache__/flickr.cpython-310.pyc,,
torchvision/datasets/__pycache__/flowers102.cpython-310.pyc,,
torchvision/datasets/__pycache__/folder.cpython-310.pyc,,
torchvision/datasets/__pycache__/food101.cpython-310.pyc,,
torchvision/datasets/__pycache__/gtsrb.cpython-310.pyc,,
torchvision/datasets/__pycache__/hmdb51.cpython-310.pyc,,
torchvision/datasets/__pycache__/imagenet.cpython-310.pyc,,
torchvision/datasets/__pycache__/inaturalist.cpython-310.pyc,,
torchvision/datasets/__pycache__/kinetics.cpython-310.pyc,,
torchvision/datasets/__pycache__/kitti.cpython-310.pyc,,
torchvision/datasets/__pycache__/lfw.cpython-310.pyc,,
torchvision/datasets/__pycache__/lsun.cpython-310.pyc,,
torchvision/datasets/__pycache__/mnist.cpython-310.pyc,,
torchvision/datasets/__pycache__/moving_mnist.cpython-310.pyc,,
torchvision/datasets/__pycache__/omniglot.cpython-310.pyc,,
torchvision/datasets/__pycache__/oxford_iiit_pet.cpython-310.pyc,,
torchvision/datasets/__pycache__/pcam.cpython-310.pyc,,
torchvision/datasets/__pycache__/phototour.cpython-310.pyc,,
torchvision/datasets/__pycache__/places365.cpython-310.pyc,,
torchvision/datasets/__pycache__/rendered_sst2.cpython-310.pyc,,
torchvision/datasets/__pycache__/sbd.cpython-310.pyc,,
torchvision/datasets/__pycache__/sbu.cpython-310.pyc,,
torchvision/datasets/__pycache__/semeion.cpython-310.pyc,,
torchvision/datasets/__pycache__/stanford_cars.cpython-310.pyc,,
torchvision/datasets/__pycache__/stl10.cpython-310.pyc,,
torchvision/datasets/__pycache__/sun397.cpython-310.pyc,,
torchvision/datasets/__pycache__/svhn.cpython-310.pyc,,
torchvision/datasets/__pycache__/ucf101.cpython-310.pyc,,
torchvision/datasets/__pycache__/usps.cpython-310.pyc,,
torchvision/datasets/__pycache__/utils.cpython-310.pyc,,
torchvision/datasets/__pycache__/video_utils.cpython-310.pyc,,
torchvision/datasets/__pycache__/vision.cpython-310.pyc,,
torchvision/datasets/__pycache__/voc.cpython-310.pyc,,
torchvision/datasets/__pycache__/widerface.cpython-310.pyc,,
torchvision/datasets/_optical_flow.py,sha256=wgbioDD5F7kl9SsyqlmBDGOvzS18A_h26ZqtEkr5394,19980
torchvision/datasets/_stereo_matching.py,sha256=2BZ8zLuG5riXlrbi8xdmZxdYPSbT3JQOhaNitYzZGTI,49999
torchvision/datasets/caltech.py,sha256=ZFJ-yA17AVDrG4mxZY5QcBgEf5oZX50_DzgMXd9_GMU,8975
torchvision/datasets/celeba.py,sha256=vAzuk7nzrml6FQd8kpGbi6eikdh6fc8oILhd24Uyyd8,8481
torchvision/datasets/cifar.py,sha256=yP0PumJE_tFnWbo3sd_QRdiEIJCFVfwR6wimPj41OJA,5956
torchvision/datasets/cityscapes.py,sha256=ZYOPir-rQjEcfYN2MAcJ9gLpouQK_9WA9b8vIbmOOOE,10459
torchvision/datasets/clevr.py,sha256=l2H_H4Jqmet68BqbgHehMZ65_GTLQkvGe9vRPfLNXLc,3504
torchvision/datasets/coco.py,sha256=fvHT5x62MhLG4pE8MUlpW-6g6_HEeizJjnIYZK3HxFw,4076
torchvision/datasets/country211.py,sha256=bn9zqsnigFSAnG893j-jTnwJ4zhrI53pFs9uuTX24TY,2459
torchvision/datasets/dtd.py,sha256=MBqZ8Yqwp5VRvPI_EvdDxb1nUeLM5QKVotSbGVzvpFc,4075
torchvision/datasets/eurosat.py,sha256=qTI24oTjJVXlfs5VHjvjC_3If9s9joobS1X7mbxswII,2111
torchvision/datasets/fakedata.py,sha256=eEiZFwVkcd0Zo-eps8AMRUkUpGKj9eQDZ6hoDxqsln0,2548
torchvision/datasets/fer2013.py,sha256=tot-6jE7_y8VRmugSUhGTPWi2O7puVyZJ90-Tb4pZ7M,2837
torchvision/datasets/fgvc_aircraft.py,sha256=f6gTdoOaeT6yJmbsunajf3abq5XsYfqHijaYaT2pPT0,4680
torchvision/datasets/flickr.py,sha256=2MUvshyI3BfmEHja9yao_Fq5pqK9A_mXxxjvf4o3Sb0,5505
torchvision/datasets/flowers102.py,sha256=be6QEIllm_-Tr3fhXohU0_mCBGOJp-IWcVFfgPZi_2k,4719
torchvision/datasets/folder.py,sha256=PdB8r5mjBZC4IGrVzRcCf7zwHF0pF3RVCJF8m1SFvDI,12227
torchvision/datasets/food101.py,sha256=E0Ikk7nswdswkjWdxN1VwOA_drD6WBOSOegsUCnluXs,3810
torchvision/datasets/gtsrb.py,sha256=3MpXIXkf9NiOa3Km75dRbjlx_uUqYtt-sQIGNKzT8gE,3845
torchvision/datasets/hmdb51.py,sha256=iQX_BQ_jdx0Oma2zCi4K9DTUZWkN6CI4ppIR_cDZxgg,6060
torchvision/datasets/imagenet.py,sha256=cPf5Lbw0I57fBkIkk_Io9R33vaSVYatdwtob0JtFPGU,8342
torchvision/datasets/inaturalist.py,sha256=G9SROR4dKUXv7K-MrR14vXFxw0aMRMmSkkPlZIf0lfA,10348
torchvision/datasets/kinetics.py,sha256=g3DEbxUZRjQou-EYSSgWrY8Ap34G-X0m19XWVnFnlUM,10580
torchvision/datasets/kitti.py,sha256=yuX-fPZN7Rs08ZFWkOXNn9EXEng0oqO8H1rsEFcPyF0,5758
torchvision/datasets/lfw.py,sha256=rRhxwkhqHXkfeybRSZ9pszAWZUhSdWzwBMyqBTiYV8Y,10746
torchvision/datasets/lsun.py,sha256=EkIlHlrmaKBLjCxKGFwYKIiYQkfsPxFWnUomGlfOchU,5842
torchvision/datasets/mnist.py,sha256=Jafwq8nwGtVo2jqSUU8oBRJrevttXfSQ-gu-rWdGC9c,21753
torchvision/datasets/moving_mnist.py,sha256=t-E3GLqVjph9kLld7iHikgucYZNNXlSAteY8iZ8Epwk,3678
torchvision/datasets/omniglot.py,sha256=th4Rfpgz0b4O-3nKjwzJz4if0Xdn0oe3v1qz5gViN7w,4193
torchvision/datasets/oxford_iiit_pet.py,sha256=ny1u1p1e3ihlpMEiYxbefFZ8hcXaHV7NZ2s7XI002JM,5178
torchvision/datasets/pcam.py,sha256=XGYE-jz3C52-PaRlU0zAJYmOK7MPGxdgJXC2e28c-Ts,5245
torchvision/datasets/phototour.py,sha256=kiBA1S-06U0qWxwgG06lVFiqRpC12z-fzD_IOo1y8VA,8152
torchvision/datasets/places365.py,sha256=zejpHPKc7qRyaaXw_GIeSxY3Z_x1f2U6NWn1YVFHtng,7369
torchvision/datasets/rendered_sst2.py,sha256=UFEfUE8AaiJkGjE6cuEdq-yfuK3c_bzAruJ7bmtSUUo,3648
torchvision/datasets/samplers/__init__.py,sha256=yX8XD3h-VwTCoqF9IdQmcuGblZxvXb2EIqc5lPqXXtY,164
torchvision/datasets/samplers/__pycache__/__init__.cpython-310.pyc,,
torchvision/datasets/samplers/__pycache__/clip_sampler.cpython-310.pyc,,
torchvision/datasets/samplers/clip_sampler.py,sha256=ERp9c0O3ZGQNR3lLXvZXWjfJkXayYym8bhYfOm_MNKI,6416
torchvision/datasets/sbd.py,sha256=Bl0uvVerxkh1vk5Wq11gWzo9jrERVsPThK5sxIai1X4,5325
torchvision/datasets/sbu.py,sha256=4HuHhYrehuY6MMZO10gDRQB8p7K9niv2uBzbVgJzxn0,4190
torchvision/datasets/semeion.py,sha256=Gsu8T1abeE0t-0lHf8TACuXh09BruHExjiw3d-bEZaQ,3179
torchvision/datasets/stanford_cars.py,sha256=-vBokYn4GHz8dvmN5GfV5teXBL1sLnYSk9MZHPV7RZI,4964
torchvision/datasets/stl10.py,sha256=oCfFz7K3jWOqaU-DiD5vQkEZ-fOKvWqUfe_fNs2xpWs,7407
torchvision/datasets/sun397.py,sha256=R09gTk7RveLMnnjFCtxhrzRdWMMwp_dSe-VYIrGcV38,2824
torchvision/datasets/svhn.py,sha256=6VW7Sk3WC_TTPiWJZac_1yKnVPp27QFfriUIm9_F6PE,4897
torchvision/datasets/ucf101.py,sha256=45HG1gpwd1CwYJhF4YJqdb83e8L88v1UHP2KMh08Yo4,5602
torchvision/datasets/usps.py,sha256=PyFECpMSh0sMswf-rZFpzy-Uyopa2BLMEYKLJgGWBRE,3535
torchvision/datasets/utils.py,sha256=TLVH3Dl-nug9ZGPxeVqDhGgKAw8fs_B_bzp-XlHdHd4,18872
torchvision/datasets/video_utils.py,sha256=wG_CYlNigFA6r4mwdvCWrzsECVOgvTFRbCL9wH8TurM,17429
torchvision/datasets/vision.py,sha256=rp3GrpyzhSOYz_AyCOZb1GrR2-8MsbZs_iy3nbX5Eyw,4245
torchvision/datasets/voc.py,sha256=QFt-ALxLyeSFi-pNM191DOt5HKQz5ET_MHGTyyddlgU,8984
torchvision/datasets/widerface.py,sha256=KQT3n9YcJkkmdYDln-JRHUSYP8t-Bvp5Bp6qs1_b8-A,8252
torchvision/extension.py,sha256=adOpOZpSCHuTrbDpipwPFR9JLoa_2fWOZRhNafpitR8,4021
torchvision/image.pyd,sha256=BkWXvV5GyOigtMfvv6FYOK0KjWWdOEsYaym-kwP5UMM,350720
torchvision/io/__init__.py,sha256=md51PMqDbCY8Wvo9rA2il7ZrZ87GshTq8fJY3xhNVOA,1547
torchvision/io/__pycache__/__init__.cpython-310.pyc,,
torchvision/io/__pycache__/_load_gpu_decoder.cpython-310.pyc,,
torchvision/io/__pycache__/_video_opt.cpython-310.pyc,,
torchvision/io/__pycache__/image.cpython-310.pyc,,
torchvision/io/__pycache__/video.cpython-310.pyc,,
torchvision/io/__pycache__/video_reader.cpython-310.pyc,,
torchvision/io/_load_gpu_decoder.py,sha256=B3mPLXerJYXqiHv9FO2laRrGRlIkXii7CsD0J8J_SwU,182
torchvision/io/_video_opt.py,sha256=n5PL4hXCnOVaLDlSfLtWzP8yK4ypwWbyvQhbSHrO0Ps,20902
torchvision/io/image.py,sha256=L1W5UTvvrMPbHBjl0fbVAuyhHCwtmCM2WL1yP9NeGE8,10142
torchvision/io/video.py,sha256=tcDKnx2z_AXAgBU5CCgthNGL2lMBDoMT-vXnfW8qyzE,16089
torchvision/io/video_reader.py,sha256=TZJkSNHxXgTFR6y4-bexQIyXvVlR-yRKKQRnEsg5X88,12041
torchvision/libjpeg.dll,sha256=RqPZ7l_awQdV8gwNfq4uF6qso45ravyjVQPBb8IOd_I,229376
torchvision/libpng16.dll,sha256=nPxu7uIrOxgpEXCLUyjP7rQBCghBVPsL-h8OxeArvc0,192512
torchvision/models/__init__.py,sha256=6QlTJfvjKcUmMJvwSapWUNFXbf2Vo15dVRcBuNSaYko,888
torchvision/models/__pycache__/__init__.cpython-310.pyc,,
torchvision/models/__pycache__/_api.cpython-310.pyc,,
torchvision/models/__pycache__/_meta.cpython-310.pyc,,
torchvision/models/__pycache__/_utils.cpython-310.pyc,,
torchvision/models/__pycache__/alexnet.cpython-310.pyc,,
torchvision/models/__pycache__/convnext.cpython-310.pyc,,
torchvision/models/__pycache__/densenet.cpython-310.pyc,,
torchvision/models/__pycache__/efficientnet.cpython-310.pyc,,
torchvision/models/__pycache__/feature_extraction.cpython-310.pyc,,
torchvision/models/__pycache__/googlenet.cpython-310.pyc,,
torchvision/models/__pycache__/inception.cpython-310.pyc,,
torchvision/models/__pycache__/maxvit.cpython-310.pyc,,
torchvision/models/__pycache__/mnasnet.cpython-310.pyc,,
torchvision/models/__pycache__/mobilenet.cpython-310.pyc,,
torchvision/models/__pycache__/mobilenetv2.cpython-310.pyc,,
torchvision/models/__pycache__/mobilenetv3.cpython-310.pyc,,
torchvision/models/__pycache__/regnet.cpython-310.pyc,,
torchvision/models/__pycache__/resnet.cpython-310.pyc,,
torchvision/models/__pycache__/shufflenetv2.cpython-310.pyc,,
torchvision/models/__pycache__/squeezenet.cpython-310.pyc,,
torchvision/models/__pycache__/swin_transformer.cpython-310.pyc,,
torchvision/models/__pycache__/vgg.cpython-310.pyc,,
torchvision/models/__pycache__/vision_transformer.cpython-310.pyc,,
torchvision/models/_api.py,sha256=lUZdXREWJUqjij-pwQUHddtKdGEsYghLs_M0D5Yg_Hg,8947
torchvision/models/_meta.py,sha256=2NSIICoq4MDzPZc00DlGJTgHOCwTBSObSTeRTh3E0tQ,30429
torchvision/models/_utils.py,sha256=X7zduE90fkek8DjukzyENOcZ0iop03R0LIxC_FuAazk,11149
torchvision/models/alexnet.py,sha256=O4Ep-XksEpZZTnw6JenT36-ruTKjCR_-r9cFGd6MLxc,4590
torchvision/models/convnext.py,sha256=Hu8U5PJAt4G2QeQGdSVRel2pVJClG1wnT0a6phXDzEs,15723
torchvision/models/densenet.py,sha256=MFGFJhVbNCbRwmwD9-ZMzLamE2hPKUqAowZnKtFy4Ok,17235
torchvision/models/detection/__init__.py,sha256=D4cs338Z4BQn5TgX2IKuJC9TD2rtw2svUDZlALR-lwI,175
torchvision/models/detection/__pycache__/__init__.cpython-310.pyc,,
torchvision/models/detection/__pycache__/_utils.cpython-310.pyc,,
torchvision/models/detection/__pycache__/anchor_utils.cpython-310.pyc,,
torchvision/models/detection/__pycache__/backbone_utils.cpython-310.pyc,,
torchvision/models/detection/__pycache__/faster_rcnn.cpython-310.pyc,,
torchvision/models/detection/__pycache__/fcos.cpython-310.pyc,,
torchvision/models/detection/__pycache__/generalized_rcnn.cpython-310.pyc,,
torchvision/models/detection/__pycache__/image_list.cpython-310.pyc,,
torchvision/models/detection/__pycache__/keypoint_rcnn.cpython-310.pyc,,
torchvision/models/detection/__pycache__/mask_rcnn.cpython-310.pyc,,
torchvision/models/detection/__pycache__/retinanet.cpython-310.pyc,,
torchvision/models/detection/__pycache__/roi_heads.cpython-310.pyc,,
torchvision/models/detection/__pycache__/rpn.cpython-310.pyc,,
torchvision/models/detection/__pycache__/ssd.cpython-310.pyc,,
torchvision/models/detection/__pycache__/ssdlite.cpython-310.pyc,,
torchvision/models/detection/__pycache__/transform.cpython-310.pyc,,
torchvision/models/detection/_utils.py,sha256=rvG1Ag0fbBwMS4SRfsFAPoMwXDlXkLtli86lrhC9R9E,22833
torchvision/models/detection/anchor_utils.py,sha256=TQKWOKDFALsTmYw_BMGIDlT9mNQhukmgnNdFuRjN49w,12127
torchvision/models/detection/backbone_utils.py,sha256=AWWKQooaLKYf8N2053Lsts-8DVlh2_IRhIjx_Y1JXes,10692
torchvision/models/detection/faster_rcnn.py,sha256=tZBXQfQbBq6ShlBfQAEtoIRGWIzvWKsIRlj1B1Y0TTI,37559
torchvision/models/detection/fcos.py,sha256=I4SvoIle9JGdgCq9EiEeIfz4ZTY8FlBczfzMimI2QIo,34744
torchvision/models/detection/generalized_rcnn.py,sha256=nLVj2o8yr6BW1MN12u30QuFbvtsdllEbUlbNH-dO-G0,4861
torchvision/models/detection/image_list.py,sha256=IzFjxIaMdyFas1IHPBgAuBK3iYJOert5HzGurYJitNk,808
torchvision/models/detection/keypoint_rcnn.py,sha256=QkV6x46pogA_p0GmdTIo2brFoS_Cg_-m5daPaM8pWpw,22215
torchvision/models/detection/mask_rcnn.py,sha256=9Gan-9MeI4DuL3HRMNFIFdVCqjstw9i7XUMi_vBIruM,27054
torchvision/models/detection/retinanet.py,sha256=LUMDvBFwnzPTvcDyJfBhIYATVq5T-6dwm2y0aawtEVw,37920
torchvision/models/detection/roi_heads.py,sha256=f_Lde69JHugGAaiGWh6ugJ9WUTT8f7InxPry_MZxgZY,34698
torchvision/models/detection/rpn.py,sha256=zQ4S0EchqmRPVUDAoKQV3O4gK2b-n-AlwyihEp6yikE,16122
torchvision/models/detection/ssd.py,sha256=uLfWWSZktTANAqa68b5uHX6SEUo5PtiA4qjNVOzl3_M,29644
torchvision/models/detection/ssdlite.py,sha256=EawR9gsl_I6RoLY5WerlrgelgJr5HXaVDQJylYK4If4,13529
torchvision/models/detection/transform.py,sha256=7lXXSEMccbMYWUsLLH6C2fdqMrrc1GA7Ez4g5rIHYaE,12150
torchvision/models/efficientnet.py,sha256=aKdKMcPNdp8_QvFgkiRVukgu6aLQlvKB7h6lyxVYStQ,44204
torchvision/models/feature_extraction.py,sha256=uTBD0Obc42BSm0YBU7VcSKJR7HDeY6Da5nOiuGBZsV8,26140
torchvision/models/googlenet.py,sha256=70hGNO2We2eXV3l8d_qcMZo0a5Qftqx32rbC3rAynK0,13134
torchvision/models/inception.py,sha256=j94iQGiVTfXV2LcfiFq5NGgtvFLxqJiYgYdVCyu3DHE,19312
torchvision/models/maxvit.py,sha256=rwmXoWfwiPm9y0CtkwNBk_AYltpjNLb5a-KaHqTe27g,32768
torchvision/models/mnasnet.py,sha256=k3_11UyNk2hZ3dvYnuiEEdrIdHhk0-dnwV8YovWpmgY,18011
torchvision/models/mobilenet.py,sha256=alrEJwktmXVcCphU8h2EAJZX0YdKfcz4tJEOdG2BXB8,217
torchvision/models/mobilenetv2.py,sha256=R2e-bKjAwnV4ouj3xK9bpL-QX1wBT4Tdkg1svxKHH04,9953
torchvision/models/mobilenetv3.py,sha256=qvWjIcz4LgDcMxKU-9xanfsxSP2EqCCIhZsM6-TMZxo,16679
torchvision/models/optical_flow/__init__.py,sha256=uuRFAdvcDobdAbY2VmxEZ7_CLH_f5-JRkCSuJRkj4RY,21
torchvision/models/optical_flow/__pycache__/__init__.cpython-310.pyc,,
torchvision/models/optical_flow/__pycache__/_utils.cpython-310.pyc,,
torchvision/models/optical_flow/__pycache__/raft.cpython-310.pyc,,
torchvision/models/optical_flow/_utils.py,sha256=PRcuU-IB6EL3hAOLiyC5q-NBzlvIKfhSF_BMplHbzfY,2125
torchvision/models/optical_flow/raft.py,sha256=0mx6oSZs6TsDw4mfxm-BL3GPu7Nt7rdzXaY-w9Ls-f8,40081
torchvision/models/quantization/__init__.py,sha256=YOJmYqWQTfP5P2ypteZNKQOMW4VEB2WHJlYoSlSaL1Y,130
torchvision/models/quantization/__pycache__/__init__.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/googlenet.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/inception.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/mobilenet.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/mobilenetv2.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/mobilenetv3.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/resnet.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/shufflenetv2.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/utils.cpython-310.pyc,,
torchvision/models/quantization/googlenet.py,sha256=GyiNPiMwODCScUSl0o9VDgRWBE0xNfHkBmLRYY2C4qo,8273
torchvision/models/quantization/inception.py,sha256=yMdcQYqYza6u7BiBOG3bei0mYeIBWljQfzrGW0Lpr3E,11071
torchvision/models/quantization/mobilenet.py,sha256=alrEJwktmXVcCphU8h2EAJZX0YdKfcz4tJEOdG2BXB8,217
torchvision/models/quantization/mobilenetv2.py,sha256=ikCLYVB5_PuHilrJEaINDT-b27l3BqXpZ3mzF_dRwos,6020
torchvision/models/quantization/mobilenetv3.py,sha256=MUvl9h6YN0t1sGLvn8lBJJlMZrB7Dn-gmRas-6JmYnU,9450
torchvision/models/quantization/resnet.py,sha256=JmwdU-hiBC_NNK5RU5jzBt2wlg1A79_PP2EeA54UOZw,18406
torchvision/models/quantization/shufflenetv2.py,sha256=3h98DAY9_1DtD12jSzM6552MjJCoqu2ckq65R2dyEDQ,17294
torchvision/models/quantization/utils.py,sha256=Ij88l6toyO8MQi1w512Jt-yQ2Q9hK75-Z2SOjIzS6Zw,2109
torchvision/models/regnet.py,sha256=41xzfdKHpWKSZskfOaApWBblXGnbIAOboLUfusnpmdk,65107
torchvision/models/resnet.py,sha256=n1E2eTjqPucXOcei_UvgR6QcdaiK6Q9fm1fZdwkp9XA,39920
torchvision/models/segmentation/__init__.py,sha256=TLL2SSmqE08HLiv_yyIWyIyrvf2xaOsZi0muDv_Y5Vc,69
torchvision/models/segmentation/__pycache__/__init__.cpython-310.pyc,,
torchvision/models/segmentation/__pycache__/_utils.cpython-310.pyc,,
torchvision/models/segmentation/__pycache__/deeplabv3.cpython-310.pyc,,
torchvision/models/segmentation/__pycache__/fcn.cpython-310.pyc,,
torchvision/models/segmentation/__pycache__/lraspp.cpython-310.pyc,,
torchvision/models/segmentation/_utils.py,sha256=yFeyBa5_Pyv1UQ_2N64XMRgTYsxifwzDd-VRP-vmIGM,1234
torchvision/models/segmentation/deeplabv3.py,sha256=mBi3xZbPA9peDLl2AmRmEHaJ5zTMJJ8-fTwZQ9K3YGI,15302
torchvision/models/segmentation/fcn.py,sha256=PNbGH1-fv-3oyQqhYK6CIOOYANuqBW-wUF_TA3rfdxM,9171
torchvision/models/segmentation/lraspp.py,sha256=pw4r2t0uXCPiRxNzomMcPWUsdH3hKGj6rTeIRXvFtO0,7804
torchvision/models/shufflenetv2.py,sha256=yivbohPZeDPQ7SiHqEGg6FQ22F4XWi-JfcoY0bB4E6Y,15825
torchvision/models/squeezenet.py,sha256=8yYQdZaOTtF3L8aInsMZpTyphMJOODUi4E7NxOwaOOQ,8969
torchvision/models/swin_transformer.py,sha256=2-SY4fGcZPA3Z0acnHX0wsCt6LDIQZhvHReDOmmk_pI,40353
torchvision/models/vgg.py,sha256=T2qC-9kCWAsl_iWLYBaaTGzS9-DSnsJSoNqF2CrL8r4,19719
torchvision/models/video/__init__.py,sha256=xHHR5c6kP0cMDXck7XnXq19iJL_Uemcxg3OC00cqE6A,97
torchvision/models/video/__pycache__/__init__.cpython-310.pyc,,
torchvision/models/video/__pycache__/mvit.cpython-310.pyc,,
torchvision/models/video/__pycache__/resnet.cpython-310.pyc,,
torchvision/models/video/__pycache__/s3d.cpython-310.pyc,,
torchvision/models/video/__pycache__/swin_transformer.cpython-310.pyc,,
torchvision/models/video/mvit.py,sha256=Rrx_fh4Y1KP98fOIb6WG8tWvEMIvltGBQsl_1hjyVHM,33746
torchvision/models/video/resnet.py,sha256=i_TpIK-mV2AIPn7q-Ss6_eqBJZk4HuS2s5aVXKptQS8,17257
torchvision/models/video/s3d.py,sha256=4pnm_VFCvUluL1r1fsXh2BfA9NTvFYc7S3G3w519xz4,8017
torchvision/models/video/swin_transformer.py,sha256=RM_mBgbSoaZM-8uswHS1g9REa_xBPzRcwok_EpFJQDQ,28414
torchvision/models/vision_transformer.py,sha256=a-9Ul9KSYa3shoJmn2Vaoeuyrasjko0-H1OJdYQSwfc,32983
torchvision/ops/__init__.py,sha256=7wibGxcF1JHDviSNs9O9Pwlc8dhMSFfZo0wzVjTFnAY,2001
torchvision/ops/__pycache__/__init__.cpython-310.pyc,,
torchvision/ops/__pycache__/_box_convert.cpython-310.pyc,,
torchvision/ops/__pycache__/_register_onnx_ops.cpython-310.pyc,,
torchvision/ops/__pycache__/_utils.cpython-310.pyc,,
torchvision/ops/__pycache__/boxes.cpython-310.pyc,,
torchvision/ops/__pycache__/ciou_loss.cpython-310.pyc,,
torchvision/ops/__pycache__/deform_conv.cpython-310.pyc,,
torchvision/ops/__pycache__/diou_loss.cpython-310.pyc,,
torchvision/ops/__pycache__/drop_block.cpython-310.pyc,,
torchvision/ops/__pycache__/feature_pyramid_network.cpython-310.pyc,,
torchvision/ops/__pycache__/focal_loss.cpython-310.pyc,,
torchvision/ops/__pycache__/giou_loss.cpython-310.pyc,,
torchvision/ops/__pycache__/misc.cpython-310.pyc,,
torchvision/ops/__pycache__/poolers.cpython-310.pyc,,
torchvision/ops/__pycache__/ps_roi_align.cpython-310.pyc,,
torchvision/ops/__pycache__/ps_roi_pool.cpython-310.pyc,,
torchvision/ops/__pycache__/roi_align.cpython-310.pyc,,
torchvision/ops/__pycache__/roi_pool.cpython-310.pyc,,
torchvision/ops/__pycache__/stochastic_depth.cpython-310.pyc,,
torchvision/ops/_box_convert.py,sha256=glF6sulLzaw_KG36wg0CHWt0ef62BnkjokbqQnBUMsU,2490
torchvision/ops/_register_onnx_ops.py,sha256=g4M5Fp7n_5ZTzIQcUXvEct3YFlUMPNVSQQfBP-J0eQQ,4288
torchvision/ops/_utils.py,sha256=xFrLnLhKDHiG2TN39tUWY-MJbLEPka6dkaVVJFAN7-8,3736
torchvision/ops/boxes.py,sha256=LtwFsa6GDIs4nSxSIpQ48sza1ucvJGfQCHmreSoul5s,15912
torchvision/ops/ciou_loss.py,sha256=Qzm89C82ehX-YvYBPLPRPhbJZdr3itizxuxrT7MLi9o,2834
torchvision/ops/deform_conv.py,sha256=HJOCXx088QJkPz442otgznhwaOkPBdbc9f5h0u1hPWk,7185
torchvision/ops/diou_loss.py,sha256=6IebWlMYc_2YnbG36niDXgM16vxajSKRfiusEuUJwpQ,3456
torchvision/ops/drop_block.py,sha256=ZkIzM1b3v5_U7z0eavzaNpN7IBq0N4ZNwwvWArispwg,6010
torchvision/ops/feature_pyramid_network.py,sha256=Xl9Ac-B1E24qp91NMGy9q4IIRF1Rl9tE-ck-hUGUy-w,8822
torchvision/ops/focal_loss.py,sha256=lS5FqgLFuDKlpm0sk5V1VgIA6LFAdJUXQaPi35nEDoU,2319
torchvision/ops/giou_loss.py,sha256=xB_RlES28k_A6iH2VqWAPBQkiT_zkEwdtREDGR_nVJM,2772
torchvision/ops/misc.py,sha256=niQnKPuifQzVXAWnnf6TkVsgetqyetjZ6lq3wi2tsZw,13892
torchvision/ops/poolers.py,sha256=sfgcZWh2dIo9UY437CnpAHdxqPQhuvjNPYzhIKlAIPE,12247
torchvision/ops/ps_roi_align.py,sha256=6_kmnE6z_3AZZ1N2hrS_uK3cbuzqZhjdM2rC50mfYUo,3715
torchvision/ops/ps_roi_pool.py,sha256=2JrjJwzVtEeEg0BebkCnGfq4xEDwMCD-Xh915mvNcyI,2940
torchvision/ops/roi_align.py,sha256=KNIJDirpCRM_hO-XNKURSTMqhSJlBuYc9rP4IltyKQY,4278
torchvision/ops/roi_pool.py,sha256=cN7rSCQfpUzETvP8SjPDl1yfXjbxg9I-tXnHbvAKya8,3015
torchvision/ops/stochastic_depth.py,sha256=9T4Zu_BaemKZafSmRwrPCVr5aaGH8tmzlsQAZO-1_-Y,2302
torchvision/transforms/__init__.py,sha256=WCNXTJUbJ1h7YaN9UfrBSvt--ST2PAV4sLICbTS-L5A,55
torchvision/transforms/__pycache__/__init__.cpython-310.pyc,,
torchvision/transforms/__pycache__/_functional_pil.cpython-310.pyc,,
torchvision/transforms/__pycache__/_functional_tensor.cpython-310.pyc,,
torchvision/transforms/__pycache__/_functional_video.cpython-310.pyc,,
torchvision/transforms/__pycache__/_presets.cpython-310.pyc,,
torchvision/transforms/__pycache__/_transforms_video.cpython-310.pyc,,
torchvision/transforms/__pycache__/autoaugment.cpython-310.pyc,,
torchvision/transforms/__pycache__/functional.cpython-310.pyc,,
torchvision/transforms/__pycache__/functional_pil.cpython-310.pyc,,
torchvision/transforms/__pycache__/functional_tensor.cpython-310.pyc,,
torchvision/transforms/__pycache__/transforms.cpython-310.pyc,,
torchvision/transforms/_functional_pil.py,sha256=kIfszOMSxPh7orQ3oiD1AhFyLy7alRpy23yGAryOxHE,12409
torchvision/transforms/_functional_tensor.py,sha256=O5wwyKkMnEkZaK9Z_CiGZJpstRlnsbQBIjQKEqyL_r8,34927
torchvision/transforms/_functional_video.py,sha256=c4BbUi3Y2LvskozFdy619piLBd5acsjxgogYAXmY5P8,3971
torchvision/transforms/_presets.py,sha256=AZ381d33VDxOE_9x2CVFxtiAKmxW6uOirKGCXv08wUI,8735
torchvision/transforms/_transforms_video.py,sha256=ub2gCT5ELiK918Bq-Pp6mzhWrAZxlj7blfpkA8Dhb1o,5124
torchvision/transforms/autoaugment.py,sha256=UD8UBlT4dWCIQaNDUDQBtc0osMHHPQluLr7seZJr4cY,28858
torchvision/transforms/functional.py,sha256=NvxXg9CYhTeAXGqYUu0vJ5tn6yk9nB1lEr3VT4EKDdo,71168
torchvision/transforms/functional_pil.py,sha256=UIc9SyotbfzE1KdN9xt6kHMIe-UlnrZJrmtRvpShXWc,386
torchvision/transforms/functional_tensor.py,sha256=Ww9_-tsjXhP2N9oigKcmcUVH8F24_rq3gniTglCn8FI,392
torchvision/transforms/transforms.py,sha256=-DMseR4b0FlKbnme4IhAN0gR18URfOnRih5g0QNx7ic,88320
torchvision/transforms/v2/__init__.py,sha256=h4qeXenpK33YXWCZXpfaV1nemv-IDUU64VqXf7Tx9u0,1589
torchvision/transforms/v2/__pycache__/__init__.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_augment.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_auto_augment.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_color.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_container.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_deprecated.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_geometry.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_meta.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_misc.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_temporal.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_transform.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_type_conversion.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_utils.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/utils.cpython-310.pyc,,
torchvision/transforms/v2/_augment.py,sha256=RZNx1cPRZA5SF4hSegM3uQaziG8zkpFDD0vdbMW3PC0,5491
torchvision/transforms/v2/_auto_augment.py,sha256=jyi6hX4o-SWd2tsthaAoFQchWz3I_enFUwAkoUtf8Lk,32385
torchvision/transforms/v2/_color.py,sha256=32gp0eSvtqgF_uyo99-zQ_NPfkQkdaR_CoceMuuhYx8,17692
torchvision/transforms/v2/_container.py,sha256=UjJfJo71dlggTEYOwqwIwNRVoKFRlgbFVM6QcQWsYD0,6161
torchvision/transforms/v2/_deprecated.py,sha256=_6DTgmBtGKGTrfhqPMvyJwXzBThb6erF5JMT6MCXw_I,1863
torchvision/transforms/v2/_geometry.py,sha256=TZNTjdpo6a09cqjHvsnyf4CbNRsX2aJUmZFPMT1cPkg,69137
torchvision/transforms/v2/_meta.py,sha256=u_-NQkNAjLUJhTTRLjC4La5foRNT1ybmE9-VwPDZsOU,3397
torchvision/transforms/v2/_misc.py,sha256=OoMMTJnNSIyR4fdqbSNjl0zVAmfKMyk94HHwLA-Dx7M,17778
torchvision/transforms/v2/_temporal.py,sha256=EO5yy5vkGyS2Tiny9V9jkMFDNEhcR-5KTZ8wPpHv6zU,1123
torchvision/transforms/v2/_transform.py,sha256=HQSGUNhirzwjbMiHl0-W-SL-VtzeO1vATAb7xLWZogY,8405
torchvision/transforms/v2/_type_conversion.py,sha256=kG45pKMnkfXdjeRJZ_4LRQRWQ2NIMANrMTvJBuEML2o,2962
torchvision/transforms/v2/_utils.py,sha256=-fcNsPCW4xdOgkgaRvcv5OxzxJ4XeCuKv4V1iA6hxXA,4055
torchvision/transforms/v2/functional/__init__.py,sha256=jdP19EoW2c7sQXfLFd6hvRuJVph2_R5MUMjLrfmjR3I,4484
torchvision/transforms/v2/functional/__pycache__/__init__.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_augment.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_color.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_deprecated.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_geometry.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_meta.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_misc.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_temporal.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_type_conversion.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_utils.cpython-310.pyc,,
torchvision/transforms/v2/functional/_augment.py,sha256=l43ePOI8Vij7zr3W_K1zMS1ys4p_FHb763vXMoIUyk4,2291
torchvision/transforms/v2/functional/_color.py,sha256=Dm9vH5KeJzBuv-bVbf5J4NFNytRLXNSlwpNnsq6ICzo,28163
torchvision/transforms/v2/functional/_deprecated.py,sha256=jhQAzagCxHt2uMGxFPhrS5MU3Y1tVsokoFjQbLs5PKE,1529
torchvision/transforms/v2/functional/_geometry.py,sha256=x2YbOBkJALbl92ikgC0Db2u7zrhgmPbXvZUoWupip90,79290
torchvision/transforms/v2/functional/_meta.py,sha256=KOTve05s18r6Wwsw2tdledTlucQVnRsPt3Xf4rvgXuk,15100
torchvision/transforms/v2/functional/_misc.py,sha256=gKCLUZ49BZCcn_omB-j1aFCW9soXpCPgMcXW5J1A83Q,6852
torchvision/transforms/v2/functional/_temporal.py,sha256=ujdAAESWkUEOsRtkEn0zkKxJRlwny0dmvYb2mwTSLRE,1198
torchvision/transforms/v2/functional/_type_conversion.py,sha256=OaBKOv4C175uahtIz9YywYElvRW7cGpHLISyDYPAYrI,989
torchvision/transforms/v2/functional/_utils.py,sha256=jwTPyQZ12t-FAU2At4WtIrIvFL7IpyPzSnfAn9w7fD4,222
torchvision/transforms/v2/utils.py,sha256=pR3lZviRJMSH9ObGVwOD9USP9LBXZ9nFo-OUQRcGt5o,2777
torchvision/utils.py,sha256=jSxupwfxQQ4duseQPevOI8EH-IxmR28Dzg7gN423BSQ,23138
torchvision/version.py,sha256=l6lJViyb6_fKQ0-Z-LkyjXZruxruRYGmz06bebvQda4,206
torchvision/zlib.dll,sha256=VMdV5rkEmaXvSaZc6UclNShv5Ns1ZaBsSlP4pIM1Mtw,87552
